/**
 * 数字人路径系统
 */
import { System } from '../../core/System';
import { Entity } from '../../core/Entity';
import { AvatarPathComponent } from '../components/AvatarPathComponent';
import { PathFollowingComponent } from '../components/PathFollowingComponent';
import { AvatarPath } from '../path/AvatarPath';
import { PathValidator } from '../path/PathValidator';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * 系统选项
 */
export interface AvatarPathSystemOptions {
  /** 是否启用调试 */
  debug?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否启用路径验证 */
  enableValidation?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 最大同时运行的路径数量 */
  maxConcurrentPaths?: number;
}

/**
 * 路径系统性能统计
 */
interface AvatarPathPerformanceStats {
  /** 活动路径数量 */
  activePathCount: number;
  /** 总路径数量 */
  totalPathCount: number;
  /** 平均更新时间 */
  averageUpdateTime: number;
  /** 最大更新时间 */
  maxUpdateTime: number;
  /** 内存使用量 */
  memoryUsage: number;
}

/**
 * 数字人路径系统
 */
export class AvatarPathSystem extends System {
  /** 路径系统选项 */
  private pathSystemOptions: AvatarPathSystemOptions;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 路径组件映射 */
  private pathComponents: Map<string, AvatarPathComponent> = new Map();
  /** 路径跟随组件映射 */
  private followingComponents: Map<string, PathFollowingComponent> = new Map();
  /** 全局路径存储 */
  private globalPaths: Map<string, AvatarPath> = new Map();
  /** 路径系统性能统计 */
  private pathPerformanceStats: AvatarPathPerformanceStats = {
    activePathCount: 0,
    totalPathCount: 0,
    averageUpdateTime: 0,
    maxUpdateTime: 0,
    memoryUsage: 0
  };
  /** 更新时间记录 */
  private updateTimes: number[] = [];
  /** 最大记录数量 */
  private readonly MAX_UPDATE_RECORDS = 100;

  /**
   * 构造函数
   * @param options 系统选项
   */
  constructor(options: AvatarPathSystemOptions = {}) {
    super(10); // 优先级10，在物理系统之后，渲染系统之前

    this.pathSystemOptions = {
      debug: false,
      updateFrequency: 60,
      enableValidation: true,
      enablePerformanceMonitoring: false,
      maxConcurrentPaths: 100,
      ...options
    };

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', '数字人路径系统初始化');
    }
  }

  /**
   * 系统初始化
   */
  public initialize(): void {
    super.initialize();

    // 监听实体添加和移除事件
    if (this.world) {
      this.world.on('entityAdded', this.onEntityAdded.bind(this));
      this.world.on('entityRemoved', this.onEntityRemoved.bind(this));
    }

    // 扫描现有实体
    this.scanExistingEntities();

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', '系统初始化完成');
    }
  }

  /**
   * 系统更新
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    const startTime = performance.now();

    // 更新所有路径组件
    this.pathComponents.forEach((component, entityId) => {
      if (component && component.isEnabled()) {
        component.update(deltaTime);
      } else if (!component) {
        this.pathComponents.delete(entityId);
      }
    });

    // 更新所有路径跟随组件
    this.followingComponents.forEach((component, entityId) => {
      if (component && component.isEnabled()) {
        component.update(deltaTime);
      } else if (!component) {
        this.followingComponents.delete(entityId);
      }
    });

    // 性能监控
    if (this.pathSystemOptions.enablePerformanceMonitoring) {
      this.updatePathPerformanceStats(performance.now() - startTime);
    }
  }

  /**
   * 添加全局路径
   * @param path 路径实例
   */
  public addGlobalPath(path: AvatarPath): void {
    // 验证路径
    if (this.pathSystemOptions.enableValidation) {
      const validation = PathValidator.validatePath(path);
      if (!validation.valid) {
        Debug.error('AvatarPathSystem', `路径验证失败: ${validation.errors.join(', ')}`);
        return;
      }
    }

    this.globalPaths.set(path.id, path);
    this.eventEmitter.emit('globalPathAdded', { path });

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', `添加全局路径: ${path.name} (${path.id})`);
    }
  }

  /**
   * 移除全局路径
   * @param pathId 路径ID
   */
  public removeGlobalPath(pathId: string): boolean {
    const path = this.globalPaths.get(pathId);
    if (!path) return false;

    this.globalPaths.delete(pathId);
    this.eventEmitter.emit('globalPathRemoved', { pathId });

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', `移除全局路径: ${path.name} (${pathId})`);
    }

    return true;
  }

  /**
   * 获取全局路径
   * @param pathId 路径ID
   */
  public getGlobalPath(pathId: string): AvatarPath | null {
    return this.globalPaths.get(pathId) || null;
  }

  /**
   * 获取所有全局路径
   */
  public getAllGlobalPaths(): AvatarPath[] {
    return Array.from(this.globalPaths.values());
  }

  /**
   * 为实体创建路径
   * @param entity 实体
   * @param pathData 路径数据
   * @returns 路径组件
   */
  public createPathForEntity(entity: Entity, pathData: any): AvatarPathComponent | null {
    // 检查实体是否已有路径组件
    let pathComponent = entity.getComponent(AvatarPathComponent.TYPE) as AvatarPathComponent;
    
    if (!pathComponent) {
      // 创建新的路径组件
      pathComponent = new AvatarPathComponent(entity, { pathData });
      entity.addComponent(pathComponent);
      
      // 注册组件
      this.pathComponents.set(entity.id, pathComponent);
      
      // 监听组件事件
      this.setupComponentEvents(pathComponent);
    } else {
      // 添加路径到现有组件
      pathComponent.addPath(pathData);
    }

    if (this.options.debug) {
      Debug.log('AvatarPathSystem', `为实体 ${entity.id} 创建路径`);
    }

    return pathComponent;
  }

  /**
   * 为实体分配全局路径
   * @param entity 实体
   * @param pathId 全局路径ID
   * @returns 是否成功分配
   */
  public assignGlobalPathToEntity(entity: Entity, pathId: string): boolean {
    const globalPath = this.globalPaths.get(pathId);
    if (!globalPath) {
      Debug.warn('AvatarPathSystem', `全局路径 ${pathId} 不存在`);
      return false;
    }

    // 克隆路径数据
    const pathData = globalPath.toJSON();
    pathData.id = `${pathId}_${entity.id}`; // 生成唯一ID
    pathData.avatarId = entity.id;

    const pathComponent = this.createPathForEntity(entity, pathData);
    return pathComponent !== null;
  }

  /**
   * 开始实体路径
   * @param entityId 实体ID
   * @param pathId 路径ID（可选）
   */
  public startEntityPath(entityId: string, pathId?: string): boolean {
    const pathComponent = this.pathComponents.get(entityId);
    if (!pathComponent) {
      Debug.warn('AvatarPathSystem', `实体 ${entityId} 没有路径组件`);
      return false;
    }

    if (pathId) {
      pathComponent.setActivePath(pathId);
    }

    pathComponent.startPath();
    return true;
  }

  /**
   * 停止实体路径
   * @param entityId 实体ID
   */
  public stopEntityPath(entityId: string): boolean {
    const pathComponent = this.pathComponents.get(entityId);
    if (!pathComponent) return false;

    pathComponent.stopPath();
    return true;
  }

  /**
   * 暂停实体路径
   * @param entityId 实体ID
   */
  public pauseEntityPath(entityId: string): boolean {
    const pathComponent = this.pathComponents.get(entityId);
    if (!pathComponent) return false;

    pathComponent.pausePath();
    return true;
  }

  /**
   * 恢复实体路径
   * @param entityId 实体ID
   */
  public resumeEntityPath(entityId: string): boolean {
    const pathComponent = this.pathComponents.get(entityId);
    if (!pathComponent) return false;

    pathComponent.resumePath();
    return true;
  }

  /**
   * 获取实体的路径组件
   * @param entityId 实体ID
   */
  public getEntityPathComponent(entityId: string): AvatarPathComponent | null {
    return this.pathComponents.get(entityId) || null;
  }

  /**
   * 获取路径系统性能统计
   */
  public getPathPerformanceStats(): AvatarPathPerformanceStats {
    return { ...this.pathPerformanceStats };
  }

  /**
   * 获取系统性能统计（重写基类方法）
   * @returns 系统性能统计
   */
  public getSystemPerformanceStats(): import('../../core/System').SystemPerformanceStats {
    // 获取基类的性能统计
    const baseStats = super.getPerformanceStats();

    // 将路径组件数量作为处理的实体数量
    baseStats.processedEntities = this.pathComponents.size;

    return baseStats;
  }

  /**
   * 扫描现有实体
   */
  private scanExistingEntities(): void {
    if (this.world) {
      this.world.getEntities().forEach(entity => {
        this.onEntityAdded(entity);
      });
    }
  }

  /**
   * 实体添加事件处理
   * @param entity 实体
   */
  private onEntityAdded(entity: Entity): void {
    // 检查是否有路径组件
    const pathComponent = entity.getComponent(AvatarPathComponent.TYPE) as AvatarPathComponent;
    if (pathComponent) {
      this.pathComponents.set(entity.id, pathComponent);
      this.setupComponentEvents(pathComponent);
    }

    // 检查是否有路径跟随组件
    const followingComponent = entity.getComponent(PathFollowingComponent.TYPE) as PathFollowingComponent;
    if (followingComponent) {
      this.followingComponents.set(entity.id, followingComponent);
    }
  }

  /**
   * 实体移除事件处理
   * @param entity 实体
   */
  private onEntityRemoved(entity: Entity): void {
    this.pathComponents.delete(entity.id);
    this.followingComponents.delete(entity.id);
  }

  /**
   * 设置组件事件监听
   * @param component 路径组件
   */
  private setupComponentEvents(component: AvatarPathComponent): void {
    component.addEventListener('pathStarted', (data) => {
      this.eventEmitter.emit('pathStarted', data);
    });

    component.addEventListener('pathCompleted', (data) => {
      this.eventEmitter.emit('pathCompleted', data);
    });

    component.addEventListener('waypointReached', (data) => {
      this.eventEmitter.emit('waypointReached', data);
    });

    component.addEventListener('triggerActivated', (data) => {
      this.eventEmitter.emit('triggerActivated', data);
    });
  }

  /**
   * 更新路径系统性能统计
   * @param updateTime 更新时间
   */
  private updatePathPerformanceStats(updateTime: number): void {
    // 记录更新时间
    this.updateTimes.push(updateTime);
    if (this.updateTimes.length > this.MAX_UPDATE_RECORDS) {
      this.updateTimes.shift();
    }

    // 计算统计数据
    this.pathPerformanceStats.activePathCount = this.pathComponents.size;
    this.pathPerformanceStats.totalPathCount = this.globalPaths.size;
    this.pathPerformanceStats.averageUpdateTime = this.updateTimes.reduce((a, b) => a + b, 0) / this.updateTimes.length;
    this.pathPerformanceStats.maxUpdateTime = Math.max(...this.updateTimes);

    // 估算内存使用量（简化计算）
    this.pathPerformanceStats.memoryUsage = (this.pathComponents.size + this.globalPaths.size) * 1024; // 假设每个路径1KB
  }

  /**
   * 监听事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 停止所有路径
    this.pathComponents.forEach(component => {
      component.stopPath();
    });

    // 清理数据
    this.pathComponents.clear();
    this.followingComponents.clear();
    this.globalPaths.clear();

    this.eventEmitter.removeAllListeners();
    super.dispose();

    if (this.pathSystemOptions.debug) {
      Debug.log('AvatarPathSystem', '系统已销毁');
    }
  }
}
